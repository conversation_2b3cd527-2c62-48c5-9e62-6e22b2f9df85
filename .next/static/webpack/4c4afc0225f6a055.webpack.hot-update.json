{"c": ["webpack"], "r": ["pages/Main/Invite", "pages/Main/Categories", "pages/Main/Cooperative"], "m": ["./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fester%2FDocuments%2FDropplace%2FDropplace-front%2Fsrc%2Fpages%2FMain%2FInvite.tsx&page=%2FMain%2FInvite!", "./src/Hooks/useToogle.ts", "./src/Modules/Auth/RecoverPassword/formSettings.ts", "./src/Modules/Main/InviteUser/formSettings.ts", "./src/Modules/Main/InviteUser/index.tsx", "./src/Modules/Main/InviteUser/styles.ts", "./src/Modules/Main/InviteUser/validation.ts", "./src/Modules/Utils/ShowSuccessMessage.tsx", "./src/pages/Main/Invite.tsx", "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fester%2FDocuments%2FDropplace%2FDropplace-front%2Fsrc%2Fpages%2FMain%2FCategories.tsx&page=%2FMain%2FCategories!", "./src/Modules/Main/Category/Components/Delete/index.tsx", "./src/Modules/Main/Category/Components/Delete/styles.ts", "./src/Modules/Main/Category/Components/Table/index.tsx", "./src/Modules/Main/Category/Components/Table/styles.ts", "./src/Modules/Main/Category/Query/useDeleteCategory.ts", "./src/Modules/Main/Category/Query/useGetCategoriesDetails.ts", "./src/Modules/Main/Category/Query/useGetCategoriesPaginated.ts", "./src/Modules/Main/Category/index.tsx", "./src/Modules/Main/Category/styles.ts", "./src/pages/Main/Categories.tsx", "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fester%2FDocuments%2FDropplace%2FDropplace-front%2Fsrc%2Fpages%2FMain%2FCooperative.tsx&page=%2FMain%2FCooperative!", "./src/Components/TitleDivider/index.tsx", "./src/Components/TitleDivider/styles.ts", "./src/Modules/Main/Cooperative/Query/useCreateOrUpdateCooperative.ts", "./src/Modules/Main/Cooperative/Query/useGetCooperativeInfo.ts", "./src/Modules/Main/Cooperative/formSettings.ts", "./src/Modules/Main/Cooperative/index.tsx", "./src/Modules/Main/Cooperative/styles.ts", "./src/Modules/Main/Cooperative/validation.ts", "./src/Modules/Utils/Zod/ZodString.ts", "./src/Utils/ValidateCNPJ.ts", "./src/pages/Main/Cooperative.tsx"]}