/* eslint-disable @typescript-eslint/no-unused-vars */
import { EProfileStatus } from '@prisma/client';
import express from 'express';
import { inject } from 'inversify';
import {
	controller,
	httpDelete,
	httpGet,
	httpPost,
	httpPut
} from 'inversify-express-utils';
import { BaseController } from 'src/api/Controllers/Base';
import generateCookieMaxAge from 'src/api/Utils/GenerateCookieMaxAge';
import { IConfirmPasswordViewModel } from 'src/api/ViewModels/User/IConfirmPassword';
import { ICreateUserViewModel } from 'src/api/ViewModels/User/ICreateUser';
import { ICreateUserSocialLoginViewModel } from 'src/api/ViewModels/User/ICreateUserSocialLogin';
import { ICreateUserWebViewModel } from 'src/api/ViewModels/User/ICreateUserWeb';
import { IDeleteUser } from 'src/api/ViewModels/User/IDeleteUser';
import { IInviteUserViewModel } from 'src/api/ViewModels/User/IInviteUser';
import { IReactivateAccountViewModel } from 'src/api/ViewModels/User/IReactivateAccount';
import { IUpdatePasswordViewModel } from 'src/api/ViewModels/User/IUpdatePassword';
import { IUpdateUserViewModel } from 'src/api/ViewModels/User/IUpdateUser';
import { IUpdateUserFrontViewModel } from 'src/api/ViewModels/User/IUpdateUserFront';
import { IUserViewModel } from 'src/api/ViewModels/User/IUser';
import { IUserProfileViewModel } from 'src/api/ViewModels/User/IUserProfile';
import { IUserProfileAnalyzeViewModel } from 'src/api/ViewModels/User/IUserProfileAnalyze';
import { IUserProfileDataViewModel } from 'src/api/ViewModels/User/IUserProfileData';
import { IUserWithAddressViewModel } from 'src/api/ViewModels/User/IUserWithAddress';
import { IUserWithProfileDataViewModel } from 'src/api/ViewModels/User/IUserWithProfileData';
import { IUserWithProfilePictureViewModel } from 'src/api/ViewModels/User/IUserWithProfilePicture';
import { IUserInfoViewModel } from 'src/api/ViewModels/User/UserInfo.vm';
import { mapper } from 'src/business/Configs/Automapper/Mapper';
import { ClientMapper } from 'src/business/Configs/Automapper/Profile/Client';
import { DeliverymanMapper } from 'src/business/Configs/Automapper/Profile/Deliveryman';
import { LoginSessionMapper } from 'src/business/Configs/Automapper/Profile/LoginSession';
import { ShopkeeperMapper } from 'src/business/Configs/Automapper/Profile/Shopkeeper';
import { UserMapper } from 'src/business/Configs/Automapper/Profile/User';
import TOKENS from 'src/business/Configs/Inversify/Tokens';
import { InvitedUserAuthenticationResult } from 'src/business/DTOs/InvitedUserAuthenticationResult';
import { IEmailVerificationStatusDTO } from 'src/business/DTOs/User/IEmailVerificationStatus';
import {
	IUserInfo,
	UserAuthenticationResult
} from 'src/business/DTOs/UserAuthenticationResult';
import { EProfile } from 'src/business/Enums/Models/EProfile';
import { CookiesFactory } from 'src/business/Interfaces/Factory/ICookiesFactory';

import { IClientService } from 'src/business/Interfaces/Service/IClient';
import { IDeliverymanService } from 'src/business/Interfaces/Service/IDeliveryman';
import { ILoginSessionService } from 'src/business/Interfaces/Service/ILoginSession';
import { IShopkeeperService } from 'src/business/Interfaces/Service/IShopkeeper';
import { IUserService } from 'src/business/Interfaces/Service/IUser';
import { INotificationManager } from 'src/business/Interfaces/Tools/INotificationManager';
import {
	Body,
	Delete,
	Get,
	Path,
	Post,
	Put,
	Query,
	Request,
	Route,
	Security,
	Tags
} from 'tsoa';

@controller('/user')
@Route('user')
@Tags('User')
export class UserController extends BaseController {
	constructor(
		@inject(TOKENS.IUserService) private userService: IUserService,
		@inject(TOKENS.CookiesFactory) private cookiesFactory: CookiesFactory,
		@inject(TOKENS.IClientService) private clientService: IClientService,
		@inject(TOKENS.IShopkeeperService)
		private shopkeeperService: IShopkeeperService,
		@inject(TOKENS.IDeliverymanService)
		private deliverymanService: IDeliverymanService,
		@inject(TOKENS.NotificationManager)
		notificationManager: INotificationManager,
		@inject(TOKENS.ILoginSessionService)
		private loginSessionService: ILoginSessionService
	) {
		super(notificationManager);
	}

	// TODO Test
	@Security('api_key')
	@httpGet('/resend-email-code/:id')
	@Get('/resend-email-code/{userId}')
	public async resendEmailVerification(
		@Request() req: express.Request,
		@Path() userId: string
	) {
		const rawToken = req.headers.authorization;

		const token = rawToken?.replace('Bearer ', '');

		if (token) {
			await this.userService.resendEmailVerification(token);
		}

		return this.customResponse<void>();
	}

	// TODO Test
	@Security('api_key')
	@httpGet('/verify-email-code/:code')
	@Get('/verify-email-code/{codeId}')
	public async verifyEmailCode(
		@Request() req: express.Request,
		@Path() codeId: string
	) {
		const { code } = req.params;

		const rawToken = req.headers.authorization;

		const token = rawToken?.replace('Bearer ', '');

		if (token) {
			await this.userService.verifyEmailCode({
				AccessToken: token,
				Code: code
			});
		}

		return this.customResponse<void>();
	}

	// TODO Test
	@Security('api_key')
	@httpGet('/get-email-status')
	@Get('/get-email-status')
	public async getEmailStatus(@Request() req: express.Request) {
		const rawToken = req.headers.authorization;

		const token = rawToken?.replace('Bearer ', '');

		let response: IEmailVerificationStatusDTO | null = null;

		if (token) {
			response = await this.userService.emailStatus(token);
		}

		return this.customResponse<IEmailVerificationStatusDTO>(response);
	}

	@Security('api_key')
	@httpGet('/with-profile/:id')
	@Get('with-profile/{userId}')
	public async getWithProfile(
		@Request() req: express.Request,
		@Path() userId: string
	) {
		const authenticateResponse = await this.authenticateRequest(req);
		if (!authenticateResponse)
			return this.customResponse(undefined, undefined, 401);

		const { id } = req.params;

		const data = await this.userService.getWithProfile(id);

		const response = mapper.map(UserMapper.IUserToIUserProfileViewModel, data);

		return this.customResponse<IUserProfileViewModel>(response);
	}

	// TODO Test this with a picture registered and fix autromapper
	@Security('api_key')
	@httpGet('/with-profile-picture/:id')
	@Get('with-profile-picture/{userId}')
	public async getWithProfilePictureById(
		@Request() req: express.Request,
		@Path() userId: string
	) {
		const authenticateResponse = await this.authenticateRequest(req);
		if (!authenticateResponse)
			return this.customResponse(undefined, undefined, 401);

		const { id } = req.params;

		const data = await this.userService.getWithProfilePictureById(id);

		const response = mapper.map(
			UserMapper.IUserWithProfilePictureDTOToIUserWithProfilePictureViewModel,
			data
		);

		return this.customResponse<IUserWithProfilePictureViewModel>(response);
	}

	@Security('api_key')
	@httpGet('/profile-data/:id')
	@Get('profile-data/{userId}')
	public async getWithProfileDataById(
		@Request() req: express.Request,
		@Path() userId: string
	) {
		const authenticateResponse = await this.authenticateRequest(req);
		if (!authenticateResponse)
			return this.customResponse(undefined, undefined, 401);

		const { id } = req.params;

		const data = await this.userService.getUserProfilesData(id);

		const response = mapper.map(
			UserMapper.IUserToIUserWithProfileDataViewModel,
			data
		);

		return this.customResponse<IUserWithProfileDataViewModel>(response);
	}

	@Security('api_key')
	@httpGet('/with-address/:id')
	@Get('with-address/{userId}')
	public async getWithAddress(
		@Request() req: express.Request,
		@Path() userId: string
	) {
		const authenticateResponse = await this.authenticateRequest(req);
		if (!authenticateResponse)
			return this.customResponse(undefined, undefined, 401);

		const { id } = req.params;

		const result = await this.userService.getWithAddress(id);

		const response = mapper.map(
			UserMapper.IUserToIUserWithAddressViewModel,
			result
		);

		return this.customResponse<IUserWithAddressViewModel>(response);
	}

	@Security('api_key')
	@httpPost('/address')
	@Post('/address')
	public async relateUserAddress(
		@Request() req: express.Request,
		@Body() body: { addressId: string }
	) {
		const authenticateResponse = await this.authenticateRequest(req);
		if (!authenticateResponse)
			return this.customResponse(undefined, undefined, 401);

		const { addressId } = req.body;

		await this.userService.relateUserAddress(
			this.userContext.userId,
			addressId
		);

		return this.customResponse(true);
	}

	// TODO Test
	@Security('api_key')
	@httpPost('/favorite-store')
	@Post('/favorite-store')
	public async relateUserFavoriteStore(
		@Request() req: express.Request,
		@Body() body: { storeId: string }
	) {
		const authenticateResponse = await this.authenticateRequest(req);
		if (!authenticateResponse)
			return this.customResponse(undefined, undefined, 401);

		const { storeId } = req.body;

		await this.userService.relateUserFavoriteStore(
			this.userContext.userId,
			storeId
		);

		return this.customResponse(true);
	}

	@Security('api_key')
	@httpDelete('/favorite-store/:storeId')
	@Delete('/favorite-store/{storeId}')
	public async deleteRelateUserFavoriteStore(
		@Request() req: express.Request,
		@Body() require: { storeId: string }
	) {
		const authenticateResponse = await this.authenticateRequest(req);
		if (!authenticateResponse)
			return this.customResponse(undefined, undefined, 401);

		const { storeId } = req.params;

		const response = await this.userService.deleteFavoriteStore(
			this.userContext.userId,
			storeId
		);

		return this.customResponse<number>(response);
	}

	// TODO Test
	@Security('api_key')
	@httpPost('/favorite-product')
	@Post('/favorite-product')
	public async relateUserFavoriteProduct(
		@Request() req: express.Request,
		@Body() require: { productId: string }
	) {
		const authenticateResponse = await this.authenticateRequest(req);
		if (!authenticateResponse)
			return this.customResponse(undefined, undefined, 401);

		const { productId } = req.body;

		await this.userService.relateUserFavoriteProduct(
			this.userContext.userId,
			productId
		);

		return this.customResponse(true);
	}

	// TODO Test
	@Security('api_key')
	@httpDelete('/favorite-product')
	@Delete('/favorite-product')
	public async deleteRelateUserFavoriteProduct(
		@Request() req: express.Request,
		@Body() require: { productId: string }
	) {
		const authenticateResponse = await this.authenticateRequest(req);
		if (!authenticateResponse)
			return this.customResponse(undefined, undefined, 401);

		const { productId } = req.body;

		const response = await this.userService.deleteFavoriteProduct(
			this.userContext.userId,
			productId
		);

		return this.customResponse<number>(response);
	}

	@httpPost('/login')
	@Post('login')
	public async login(
		@Request() req: express.Request,
		@Body() require: { email: string; password: string }
	) {
		const { email, password } = req.body;

		const result = await this.userService.login({
			email,
			password
		});

		return this.customResponse<
			UserAuthenticationResult | InvitedUserAuthenticationResult
		>(result);
	}

	@httpPost('/login-web')
	@Post('login-web')
	public async loginWeb(
		@Request() req: express.Request,
		@Body() require: { email: string; password: string }
	) {
		const { email, password } = req.body;

		let response: { userInfo: IUserInfo } | string | null = null;

		const result = await this.userService.login({
			email,
			password,
			origin: 'web'
		});

		const cookies = this.cookiesFactory();

		if (result && 'cognitoAuthenticationResult' in result) {
			if (
				result?.cognitoAuthenticationResult?.AccessToken &&
				result.cognitoAuthenticationResult.ExpiresIn
			) {
				cookies.setCookie({
					name: 'authorization',
					value: result.cognitoAuthenticationResult.AccessToken,
					options: {
						httpOnly: true,
						/* secure: true, */
						maxAge: generateCookieMaxAge(
							result.cognitoAuthenticationResult.ExpiresIn
						)
					}
				});

				cookies.setCookie({
					name: 'refreshToken',
					value: result.cognitoAuthenticationResult.RefreshToken,
					options: {
						httpOnly: true
						/* secure: true, */
						// maxAge: generateCookieMaxAge(response.cognitoAuthenticationResult.ExpiresIn),
					}
				});

				cookies.setCookie({
					name: 'idToken',
					value: result.cognitoAuthenticationResult.IdToken,
					options: {
						httpOnly: true
					}
				});

				cookies.setCookie({
					name: 'authenticated',
					value: 'true',
					options: {
						httpOnly: false,
						/* secure: true, */
						maxAge: generateCookieMaxAge(
							result.cognitoAuthenticationResult.ExpiresIn
						)
					}
				});
			}
		}

		if (result && 'Session' in result && result.Session !== undefined) {
			Object.entries(result).forEach(([key, value]) => {
				cookies.setCookie({
					name: key,
					value: value as string,
					options: {
						httpOnly: true,
						/* secure: true, */
						maxAge: generateCookieMaxAge(60 * 5)
					}
				});
			});

			cookies.setCookie({
				name: 'userStatus',
				value: 'UPDATE_REQUIRED',
				options: { httpOnly: false, maxAge: generateCookieMaxAge(60 * 5) }
			});

			response = 'Ok';
		}

		if (result && 'userInfo' in result) {
			response = { userInfo: result.userInfo };
		}

		return this.customResponse<{ userInfo: IUserInfo } | string>(
			response,
			cookies.getAll()
		);
	}

	@Security('api_key')
	@httpGet('/by-email/:email')
	@Get('by-email/:email')
	public async getByEmail(
		@Request() req: express.Request,
		@Path() email: string
	) {
		const authenticateResponse = await this.authenticateRequest(req);
		if (!authenticateResponse)
			return this.customResponse(undefined, undefined, 401);

		const queryEmail: string = req.params.email;

		const user = await this.userService.getByEmail(queryEmail);

		const response = mapper.map(UserMapper.IUserToIUserViewModel, user);

		return this.customResponse<IUserViewModel>(response);
	}

	@Security('api_key')
	@httpGet('/notification-quantity')
	@Get('notification-quantity')
	public async getNotificationQuantity(
		@Request() req: express.Request,
		@Query() profile: EProfile
	) {
		const authenticateResponse = await this.authenticateRequest(req);
		if (!authenticateResponse)
			return this.customResponse(undefined, undefined, 401);

		const queryData = req.query;

		const quantityNotification = await this.userService.getQuantityNotification(
			this.userContext.userId,
			queryData.profile as EProfile
		);

		return this.customResponse<number>(quantityNotification);
	}

	@Security('api_key')
	@httpGet('/')
	@Get('/')
	public async get(@Request() req: express.Request) {
		const authenticateResponse = await this.authenticateRequest(req);
		if (!authenticateResponse)
			return this.customResponse(undefined, undefined, 401);

		const user = await this.userService.getById(this.userContext.userId);

		const response = mapper.map(UserMapper.IUserToIUserViewModel, user);

		return this.customResponse<IUserViewModel>(response);
	}

	@httpPost('/')
	@Post('/')
	public async create(
		@Request() req: express.Request,
		@Body() user: ICreateUserViewModel
	) {
		const data = req.body;

		const input = mapper.map(
			UserMapper.ICreateUserViewModelToIUserRegisterDTO,
			data
		);

		const response = await this.userService.register(input);

		return this.customResponse<UserAuthenticationResult>(response);
	}

	// TODO
	/**
	 * The user session and temporary password can be used only once, so we need to handle this case
	 * and have a new routine to resend a new the temporary password and the user session
	 */
	@httpPost('/web')
	@Post('/web')
	public async createWeb(
		@Request() req: express.Request,
		@Body() user: ICreateUserWebViewModel
	) {
		let response: UserAuthenticationResult | null = null;
		const data = req.body;
		const { cookies } = req;

		const responseCookies = this.cookiesFactory();

		if (
			cookies.ChallengeName &&
			cookies.ChallengeParameters &&
			cookies.Session
		) {
			data.authChallengeData = {
				ChallengeName: cookies.ChallengeName,
				ChallengeParameters: cookies.ChallengeParameters,
				Session: cookies.Session
			};

			const input = mapper.map(
				UserMapper.ICreateUserWebViewModelToIUserRegisterDTO,
				data
			);

			response = await this.userService.register(input, 'web');

			if (
				response &&
				response.cognitoAuthenticationResult &&
				response.cognitoAuthenticationResult.AccessToken &&
				response.cognitoAuthenticationResult.RefreshToken &&
				response.cognitoAuthenticationResult.ExpiresIn
			) {
				responseCookies.setCookie({
					name: 'authorization',
					value: response.cognitoAuthenticationResult.AccessToken,
					options: {
						httpOnly: true,
						/* secure: true, */
						maxAge: generateCookieMaxAge(
							response.cognitoAuthenticationResult.ExpiresIn
						)
					}
				});

				responseCookies.setCookie({
					name: 'refreshToken',
					value: response.cognitoAuthenticationResult.RefreshToken,
					options: {
						httpOnly: true,
						/* secure: true, */
						maxAge: generateCookieMaxAge(
							response.cognitoAuthenticationResult.ExpiresIn
						)
					}
				});
			}
		} else {
			this.notificationManager.add('generic.errors', 'missing_credentials');
		}

		return this.customResponse<UserAuthenticationResult['userInfo']>(
			response?.userInfo,
			responseCookies.getAll()
		);
	}

	@Security('api_key')
	@httpPost('/invite')
	@Post('/invite')
	public async invite(
		@Request() req: express.Request,
		@Body() userEmail: IInviteUserViewModel
	) {
		const authenticateResponse = await this.authenticateRequest(req);
		if (!authenticateResponse)
			return this.customResponse(undefined, undefined, 401);

		const { email } = req.body;

		const response = await this.userService.invite(email);

		return this.customResponse(response);
	}

	// TODO Test
	@httpPost('/social-login')
	@Post('/social-login')
	public async createUserBySocialLogin(
		@Request() req: express.Request,
		@Body() user: ICreateUserSocialLoginViewModel
	) {
		const authenticateResponse = await this.authenticateRequest(req);
		if (!authenticateResponse)
			return this.customResponse(undefined, undefined, 401);

		const userData = req.body;

		const input = mapper.map(
			UserMapper.ICreateUserSocialLoginViewModelToIUser,
			userData
		);

		const response = await this.userService.createUserBySocialLogin(input);

		return this.customResponse<IUserInfoViewModel>(response);
	}

	// TODO Test
	@httpPost('/notify-social-login')
	@Post('/notify-social-login')
	public async notifySocialLoginUser(@Request() req: express.Request) {
		const authenticateResponse = await this.authenticateRequest(req);
		if (!authenticateResponse)
			return this.customResponse(undefined, undefined, 401);

		const { userId } = this.userContext;

		if (userId) {
			await this.userService.notifySocialLoginUser(userId);
		}

		return this.customResponse('ok');
	}

	@Security('api_key')
	@httpPut('/profile')
	@Put('/profile')
	public async updateProfile(
		@Request() req: express.Request,
		@Body() profileData: IUserProfileDataViewModel
	) {
		const authenticateResponse = await this.authenticateRequest(req);
		if (!authenticateResponse)
			return this.customResponse(undefined, undefined, 401);

		const data: IUserProfileDataViewModel = req.body;

		const id = this.validateId(this.userContext.userId);

		if (!id) return null;

		if (data.client) {
			const input = mapper.map(
				ClientMapper.IClientViewModelToIClient,
				data.client
			);

			await this.clientService.updateClientProfile(id, input);
		}

		if (data.shopkeeper) {
			const input = mapper.map(
				ShopkeeperMapper.IShopkeeperViewModelToIShopkeeper,
				data.shopkeeper
			);

			await this.shopkeeperService.updateShopkeeperProfile(
				id,
				input,
				data.shopkeeper.attachments
			);
		}

		if (data.deliveryman) {
			const input = mapper.map(
				DeliverymanMapper.IDeliverymanViewModelToIDeliveryman,
				data.deliveryman
			);

			await this.deliverymanService.updateDeliverymanProfile(
				id,
				input,
				data.deliveryman.attachments
			);
		}

		return this.customResponse(true);
	}

	@Security('api_key')
	@httpPut('/')
	@Put('/')
	public async update(
		@Request() req: express.Request,
		@Body() user: IUpdateUserViewModel
	) {
		const authenticateResponse = await this.authenticateRequest(req);
		if (!authenticateResponse)
			return this.customResponse(undefined, undefined, 401);
		const data = req.body;
		const updatedByADeliveryMan = data.pixKey !== null || data.pixKey === '';
		if (updatedByADeliveryMan) {
			const { id, pixKey } = data;
			const result = await this.deliverymanService.updateDeliverymanPixKey(
				id,
				pixKey
			);
		}
		const input = mapper.map(UserMapper.IUpdateUserViewModelToIUser, data);

		const response = await this.userService.updateUser(input);

		return this.customResponse<number>(response);
	}

	@Security('api_key')
	@httpDelete('/:id')
	@Delete('{userId}')
	public async delete(@Request() req: express.Request, @Path() userId: string) {
		const authenticateResponse = await this.authenticateRequest(req);
		if (!authenticateResponse)
			return this.customResponse(undefined, undefined, 401);

		const { id } = req.params;

		const result = await this.userService.delete(id);

		return this.customResponse<boolean>(result);
	}

	@httpPost('/reset-password')
	@Post('/reset-password')
	public async resetPassword(
		@Request() req: express.Request,
		@Body() credentials: { email: string }
	) {
		const { email } = req.body;

		const response = await this.userService.resetPassword(email);

		return this.customResponse<boolean>(response);
	}

	@httpPost('/confirm-password')
	@Post('/confirm-password')
	public async confirmPassword(
		@Request() req: express.Request,
		@Body() data: IConfirmPasswordViewModel
	) {
		const { email, verificationCode, newPassword } = req.body;

		const response = await this.userService.confirmPassword(
			email,
			verificationCode,
			newPassword
		);

		return this.customResponse<boolean>(response);
	}

	@httpPut('/update-password')
	@Put('/update-password')
	public async updatePassword(
		@Request() req: express.Request,
		@Body() body: IUpdatePasswordViewModel
	) {
		const authenticateResponse = await this.authenticateRequest(req);
		if (!authenticateResponse)
			return this.customResponse(undefined, undefined, 401);

		const data = req.body;

		const rawToken = req.headers.authorization;

		const token = rawToken!.replace('Bearer ', '');

		let response: boolean | null = null;

		response = await this.userService.updatePassword(
			token,
			data.previousPassword,
			data.proposedPassword
		);

		return this.customResponse<boolean>(response);
	}

	@httpPost('/login-session')
	@Post('/login-session')
	public async loginSession(@Request() req: express.Request) {
		const authenticateResponse = await this.authenticateRequest(req);
		if (!authenticateResponse)
			return this.customResponse(undefined, undefined, 401);

		const { userId } = this.userContext;

		const input = mapper.map(
			LoginSessionMapper.ILoginSessionViewModelToILoginSession,
			{ userId }
		);

		const response = await this.loginSessionService.create(input);

		return this.customResponse<boolean>(response);
	}

	@httpPut('/update-session-last-access')
	@Put('/update-session-last-access')
	public async updateSessionLastAccess(@Request() req: express.Request) {
		const authenticateResponse = await this.authenticateRequest(req);
		if (!authenticateResponse)
			return this.customResponse(undefined, undefined, 401);

		const response = await this.loginSessionService.updateLastAccess(
			this.userContext.userId
		);

		return this.customResponse<boolean>(response);
	}

	@Security('api_key')
	@httpGet('/all-users/')
	@Get('all-users/')
	public async getAllUsers(
		@Request() req: express.Request,
		@Query('page') page: number,
		@Query('pageSize') pageSize: number,
		@Query('filterName') filterName: string,
		@Query('filterProfile') filterProfile: string,
		@Query('filterStatus') filterStatus: string,
		@Query('sortDirection') sortDirection?: string
	) {
		const authenticateResponse = await this.authenticateRequest(req);
		if (!authenticateResponse)
			return this.customResponse(undefined, undefined, 401);
		const queryData = req.query as unknown as {
			page: string;
			pageSize: string;
			filterName?: string;
			filterProfile?: string;
			filterStatus?: EProfileStatus;
			sortDirection?: string;
		};

		const { result, totalCount, totalPages } =
			await this.userService.getAllPaged(
				queryData.page,
				queryData.pageSize,
				queryData.filterName,
				queryData.filterProfile,
				queryData.filterStatus,
				queryData.sortDirection
			);

		const response = {
			result: mapper.map(
				UserMapper.IUserToIUserProfileAnalyzeViewModel,
				result
			),
			totalCount,
			totalPages
		};

		return this.customResponse<{
			result: IUserProfileAnalyzeViewModel[];
			totalCount: number;
			totalPages: number;
		}>(response);
	}

	@Security('api_key')
	@httpPut('/by-user-id/:userId')
	@Put('by-user-id/{userId}')
	public async updateByUserId(
		@Request() req: express.Request,
		@Path() userId: string,
		@Body() user: IUpdateUserFrontViewModel
	) {
		const authenticateResponse = await this.authenticateRequest(req);
		if (!authenticateResponse)
			return this.customResponse(undefined, undefined, 401);
		const data: IUpdateUserFrontViewModel = req.body;
		const userIdQ = req.params.userId as string;

		const result = await this.userService.updateByUserId(userIdQ, data);

		return this.customResponse<boolean>(result);
	}

	@Security('api_key')
	@httpDelete('/users/delete/:userId') // REVIEW |-> user/users is redundant - Is not being used <-|
	@Delete('users/delete/{userId}')
	public async deleteByUserId(
		@Request() req: express.Request,
		@Path() userId: string
	) {
		const authenticateResponse = await this.authenticateRequest(req);
		if (!authenticateResponse)
			return this.customResponse(undefined, undefined, 401);
		const { id } = req.params;

		const result = await this.userService.deleteByUserId(id);

		return this.customResponse<boolean>(result);
	}

	@Security('api_key')
	@httpPut('/disable/')
	@Put('disable')
	public async disableUser(
		@Request() req: express.Request,
		@Body() user: IDeleteUser
	) {
		const authenticateResponse = await this.authenticateRequest(req);
		if (!authenticateResponse)
			return this.customResponse(undefined, undefined, 401);
		const data: IDeleteUser = req.body;

		const result = await this.userService.disableUser(data);

		return this.customResponse<boolean>(true);
	}

	@Security('api_key')
	@httpPost('/temporally-deleted/')
	@Post('temporally-deleted/')
	public async getByTemporallyDeletedStatus(
		@Request() req: express.Request,
		@Body() user: { email: string }
	) {
		const { email } = req.body;

		const output = await this.userService.getByTemporallyDeletedStatus(email);

		return this.customResponse<boolean>(output);
	}

	@Security('api_key')
	@httpPost('/reactivate-deleted-account/')
	@Post('reactivate-deleted-account/')
	public async updateDeletedAndReactivateAccount(
		@Request() req: express.Request,
		@Body() user: IReactivateAccountViewModel
	) {
		const input: IReactivateAccountViewModel = req.body;
		const result = await this.userService.resendEmailConfirmationCode(input);

		return this.customResponse<boolean>(result);
	}

	@Security('api_key')
	@httpPost('/confirm-reactivate-account/')
	@Post('/confirm-reactivate-account/')
	public async confirmReactivateAccountCode(
		@Request() req: express.Request,
		@Body() body: IReactivateAccountViewModel
	) {
		const input: IReactivateAccountViewModel = req.body;

		const output = await this.userService.confirmSignUpCode(input);

		return this.customResponse<boolean>(output);
	}

	@Security('api_key')
	@httpDelete('/cognito/:username')
	@Delete('cognito/{username}')
	public async deleteCognitoUser(
		@Request() req: express.Request,
		@Path() username: string
	) {
		const authenticateResponse = await this.authenticateRequest(req);
		if (!authenticateResponse)
			return this.customResponse(undefined, undefined, 401);

		const { username: cognitoUsername } = req.params;

		const result = await this.userService.deleteCognitoUser(cognitoUsername);

		return this.customResponse<boolean>(result);
	}

	@Security('api_key')
	@httpPost('/delete-cognito-user')
	@Post('delete-cognito-user')
	public async deleteCognitoUserByBody(
		@Request() req: express.Request,
		@Body() body: { username: string }
	) {
		const authenticateResponse = await this.authenticateRequest(req);
		if (!authenticateResponse)
			return this.customResponse(undefined, undefined, 401);

		const { username } = req.body;

		const result = await this.userService.deleteCognitoUser(username);

		return this.customResponse<boolean>(result);
	}

	@Security('api_key')
	@httpPost('/check-registered-email/')
	@Post('check-registered-email/')
	public async checkRegisteredEmail(
		@Request() req: express.Request,
		@Body() user: { email: string }
	) {
		const { email } = req.body;

		const userRegistered = await this.userService.getByEmail(email);

		return this.customResponse<boolean>(!!userRegistered);
	}

	@Security('api_key')
	@httpPost('/check-registered-cpf/')
	@Post('check-registered-cpf/')
	public async checkRegisteredCpf(
		@Request() req: express.Request,
		@Body() user: { cpf: string }
	) {
		const { cpf } = req.body;

		const userRegistered = await this.userService.getByCpf(cpf);

		return this.customResponse<boolean>(!!userRegistered);
	}

	@Security('api_key')
	@httpGet('/details/:id')
	@Get('/details/{id}')
	public async getUserDetailsBackOffice(@Request() req: express.Request) {
		const authenticateResponse = await this.authenticateRequest(req);
		if (!authenticateResponse)
			return this.customResponse(undefined, undefined, 401);

		const user = await this.userService.getUserDetailsBackOffice(req.params.id);

		const response = mapper.map(
			UserMapper.IUserToIUserProfileAnalyzeViewModel,
			user
		);

		return this.customResponse<IUserProfileAnalyzeViewModel>(response);
	}
}
