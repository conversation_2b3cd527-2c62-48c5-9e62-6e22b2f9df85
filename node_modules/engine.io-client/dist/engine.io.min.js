/*!
 * Engine.IO v6.4.0
 * (c) 2014-2023 <PERSON>
 * Released under the MIT License.
 */
!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):(e="undefined"!=typeof globalThis?globalThis:e||self).eio=t()}(this,(function(){"use strict";function e(t){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e(t)}function t(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function r(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function n(e,t,n){return t&&r(e.prototype,t),n&&r(e,n),e}function o(){return o=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},o.apply(this,arguments)}function i(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&a(e,t)}function s(e){return s=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)},s(e)}function a(e,t){return a=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e},a(e,t)}function u(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}function c(e,t,r){return c=u()?Reflect.construct:function(e,t,r){var n=[null];n.push.apply(n,t);var o=new(Function.bind.apply(e,n));return r&&a(o,r.prototype),o},c.apply(null,arguments)}function p(e){var t="function"==typeof Map?new Map:void 0;return p=function(e){if(null===e||(r=e,-1===Function.toString.call(r).indexOf("[native code]")))return e;var r;if("function"!=typeof e)throw new TypeError("Super expression must either be null or a function");if(void 0!==t){if(t.has(e))return t.get(e);t.set(e,n)}function n(){return c(e,arguments,s(this).constructor)}return n.prototype=Object.create(e.prototype,{constructor:{value:n,enumerable:!1,writable:!0,configurable:!0}}),a(n,e)},p(e)}function h(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function l(e,t){return!t||"object"!=typeof t&&"function"!=typeof t?h(e):t}function f(e){var t=u();return function(){var r,n=s(e);if(t){var o=s(this).constructor;r=Reflect.construct(n,arguments,o)}else r=n.apply(this,arguments);return l(this,r)}}function d(e,t,r){return d="undefined"!=typeof Reflect&&Reflect.get?Reflect.get:function(e,t,r){var n=function(e,t){for(;!Object.prototype.hasOwnProperty.call(e,t)&&null!==(e=s(e)););return e}(e,t);if(n){var o=Object.getOwnPropertyDescriptor(n,t);return o.get?o.get.call(r):o.value}},d(e,t,r||e)}var y=Object.create(null);y.open="0",y.close="1",y.ping="2",y.pong="3",y.message="4",y.upgrade="5",y.noop="6";var v=Object.create(null);Object.keys(y).forEach((function(e){v[y[e]]=e}));for(var m={type:"error",data:"parser error"},g="function"==typeof Blob||"undefined"!=typeof Blob&&"[object BlobConstructor]"===Object.prototype.toString.call(Blob),b="function"==typeof ArrayBuffer,k=function(e,t,r){var n,o=e.type,i=e.data;return g&&i instanceof Blob?t?r(i):w(i,r):b&&(i instanceof ArrayBuffer||(n=i,"function"==typeof ArrayBuffer.isView?ArrayBuffer.isView(n):n&&n.buffer instanceof ArrayBuffer))?t?r(i):w(new Blob([i]),r):r(y[o]+(i||""))},w=function(e,t){var r=new FileReader;return r.onload=function(){var e=r.result.split(",")[1];t("b"+e)},r.readAsDataURL(e)},T="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",R="undefined"==typeof Uint8Array?[]:new Uint8Array(256),x=0;x<T.length;x++)R[T.charCodeAt(x)]=x;var S="function"==typeof ArrayBuffer,O=function(e,t){if("string"!=typeof e)return{type:"message",data:B(e,t)};var r=e.charAt(0);return"b"===r?{type:"message",data:E(e.substring(1),t)}:v[r]?e.length>1?{type:v[r],data:e.substring(1)}:{type:v[r]}:m},E=function(e,t){if(S){var r=function(e){var t,r,n,o,i,s=.75*e.length,a=e.length,u=0;"="===e[e.length-1]&&(s--,"="===e[e.length-2]&&s--);var c=new ArrayBuffer(s),p=new Uint8Array(c);for(t=0;t<a;t+=4)r=R[e.charCodeAt(t)],n=R[e.charCodeAt(t+1)],o=R[e.charCodeAt(t+2)],i=R[e.charCodeAt(t+3)],p[u++]=r<<2|n>>4,p[u++]=(15&n)<<4|o>>2,p[u++]=(3&o)<<6|63&i;return c}(e);return B(r,t)}return{base64:!0,data:e}},B=function(e,t){return"blob"===t&&e instanceof ArrayBuffer?new Blob([e]):e},L=String.fromCharCode(30);function P(e){if(e)return function(e){for(var t in P.prototype)e[t]=P.prototype[t];return e}(e)}P.prototype.on=P.prototype.addEventListener=function(e,t){return this._callbacks=this._callbacks||{},(this._callbacks["$"+e]=this._callbacks["$"+e]||[]).push(t),this},P.prototype.once=function(e,t){function r(){this.off(e,r),t.apply(this,arguments)}return r.fn=t,this.on(e,r),this},P.prototype.off=P.prototype.removeListener=P.prototype.removeAllListeners=P.prototype.removeEventListener=function(e,t){if(this._callbacks=this._callbacks||{},0==arguments.length)return this._callbacks={},this;var r,n=this._callbacks["$"+e];if(!n)return this;if(1==arguments.length)return delete this._callbacks["$"+e],this;for(var o=0;o<n.length;o++)if((r=n[o])===t||r.fn===t){n.splice(o,1);break}return 0===n.length&&delete this._callbacks["$"+e],this},P.prototype.emit=function(e){this._callbacks=this._callbacks||{};for(var t=new Array(arguments.length-1),r=this._callbacks["$"+e],n=1;n<arguments.length;n++)t[n-1]=arguments[n];if(r){n=0;for(var o=(r=r.slice(0)).length;n<o;++n)r[n].apply(this,t)}return this},P.prototype.emitReserved=P.prototype.emit,P.prototype.listeners=function(e){return this._callbacks=this._callbacks||{},this._callbacks["$"+e]||[]},P.prototype.hasListeners=function(e){return!!this.listeners(e).length};var C="undefined"!=typeof self?self:"undefined"!=typeof window?window:Function("return this")();function q(e){for(var t=arguments.length,r=new Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];return r.reduce((function(t,r){return e.hasOwnProperty(r)&&(t[r]=e[r]),t}),{})}var A=C.setTimeout,j=C.clearTimeout;function _(e,t){t.useNativeTimers?(e.setTimeoutFn=A.bind(C),e.clearTimeoutFn=j.bind(C)):(e.setTimeoutFn=C.setTimeout.bind(C),e.clearTimeoutFn=C.clearTimeout.bind(C))}var U,H=function(e){i(n,e);var r=f(n);function n(e,o,i){var s;return t(this,n),(s=r.call(this,e)).description=o,s.context=i,s.type="TransportError",s}return n}(p(Error)),F=function(e){i(o,e);var r=f(o);function o(e){var n;return t(this,o),(n=r.call(this)).writable=!1,_(h(n),e),n.opts=e,n.query=e.query,n.socket=e.socket,n}return n(o,[{key:"onError",value:function(e,t,r){return d(s(o.prototype),"emitReserved",this).call(this,"error",new H(e,t,r)),this}},{key:"open",value:function(){return this.readyState="opening",this.doOpen(),this}},{key:"close",value:function(){return"opening"!==this.readyState&&"open"!==this.readyState||(this.doClose(),this.onClose()),this}},{key:"send",value:function(e){"open"===this.readyState&&this.write(e)}},{key:"onOpen",value:function(){this.readyState="open",this.writable=!0,d(s(o.prototype),"emitReserved",this).call(this,"open")}},{key:"onData",value:function(e){var t=O(e,this.socket.binaryType);this.onPacket(t)}},{key:"onPacket",value:function(e){d(s(o.prototype),"emitReserved",this).call(this,"packet",e)}},{key:"onClose",value:function(e){this.readyState="closed",d(s(o.prototype),"emitReserved",this).call(this,"close",e)}},{key:"pause",value:function(e){}}]),o}(P),D="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz-_".split(""),M={},I=0,W=0;function N(e){var t="";do{t=D[e%64]+t,e=Math.floor(e/64)}while(e>0);return t}function X(){var e=N(+new Date);return e!==U?(I=0,U=e):e+"."+N(I++)}for(;W<64;W++)M[D[W]]=W;function $(e){var t="";for(var r in e)e.hasOwnProperty(r)&&(t.length&&(t+="&"),t+=encodeURIComponent(r)+"="+encodeURIComponent(e[r]));return t}function z(e){for(var t={},r=e.split("&"),n=0,o=r.length;n<o;n++){var i=r[n].split("=");t[decodeURIComponent(i[0])]=decodeURIComponent(i[1])}return t}var V=!1;try{V="undefined"!=typeof XMLHttpRequest&&"withCredentials"in new XMLHttpRequest}catch(e){}var G=V;function J(e){var t=e.xdomain;try{if("undefined"!=typeof XMLHttpRequest&&(!t||G))return new XMLHttpRequest}catch(e){}if(!t)try{return new(C[["Active"].concat("Object").join("X")])("Microsoft.XMLHTTP")}catch(e){}}function K(){}var Q=null!=new J({xdomain:!1}).responseType,Y=function(e){i(s,e);var r=f(s);function s(e){var n;if(t(this,s),(n=r.call(this,e)).polling=!1,"undefined"!=typeof location){var o="https:"===location.protocol,i=location.port;i||(i=o?"443":"80"),n.xd="undefined"!=typeof location&&e.hostname!==location.hostname||i!==e.port,n.xs=e.secure!==o}var a=e&&e.forceBase64;return n.supportsBinary=Q&&!a,n}return n(s,[{key:"doOpen",value:function(){this.poll()}},{key:"pause",value:function(e){var t=this;this.readyState="pausing";var r=function(){t.readyState="paused",e()};if(this.polling||!this.writable){var n=0;this.polling&&(n++,this.once("pollComplete",(function(){--n||r()}))),this.writable||(n++,this.once("drain",(function(){--n||r()})))}else r()}},{key:"poll",value:function(){this.polling=!0,this.doPoll(),this.emitReserved("poll")}},{key:"onData",value:function(e){var t=this;(function(e,t){for(var r=e.split(L),n=[],o=0;o<r.length;o++){var i=O(r[o],t);if(n.push(i),"error"===i.type)break}return n})(e,this.socket.binaryType).forEach((function(e){if("opening"===t.readyState&&"open"===e.type&&t.onOpen(),"close"===e.type)return t.onClose({description:"transport closed by the server"}),!1;t.onPacket(e)})),"closed"!==this.readyState&&(this.polling=!1,this.emitReserved("pollComplete"),"open"===this.readyState&&this.poll())}},{key:"doClose",value:function(){var e=this,t=function(){e.write([{type:"close"}])};"open"===this.readyState?t():this.once("open",t)}},{key:"write",value:function(e){var t=this;this.writable=!1,function(e,t){var r=e.length,n=new Array(r),o=0;e.forEach((function(e,i){k(e,!1,(function(e){n[i]=e,++o===r&&t(n.join(L))}))}))}(e,(function(e){t.doWrite(e,(function(){t.writable=!0,t.emitReserved("drain")}))}))}},{key:"uri",value:function(){var e=this.query||{},t=this.opts.secure?"https":"http",r="";!1!==this.opts.timestampRequests&&(e[this.opts.timestampParam]=X()),this.supportsBinary||e.sid||(e.b64=1),this.opts.port&&("https"===t&&443!==Number(this.opts.port)||"http"===t&&80!==Number(this.opts.port))&&(r=":"+this.opts.port);var n=$(e);return t+"://"+(-1!==this.opts.hostname.indexOf(":")?"["+this.opts.hostname+"]":this.opts.hostname)+r+this.opts.path+(n.length?"?"+n:"")}},{key:"request",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return o(e,{xd:this.xd,xs:this.xs},this.opts),new Z(this.uri(),e)}},{key:"doWrite",value:function(e,t){var r=this,n=this.request({method:"POST",data:e});n.on("success",t),n.on("error",(function(e,t){r.onError("xhr post error",e,t)}))}},{key:"doPoll",value:function(){var e=this,t=this.request();t.on("data",this.onData.bind(this)),t.on("error",(function(t,r){e.onError("xhr poll error",t,r)})),this.pollXhr=t}},{key:"name",get:function(){return"polling"}}]),s}(F),Z=function(e){i(o,e);var r=f(o);function o(e,n){var i;return t(this,o),_(h(i=r.call(this)),n),i.opts=n,i.method=n.method||"GET",i.uri=e,i.async=!1!==n.async,i.data=void 0!==n.data?n.data:null,i.create(),i}return n(o,[{key:"create",value:function(){var e=this,t=q(this.opts,"agent","pfx","key","passphrase","cert","ca","ciphers","rejectUnauthorized","autoUnref");t.xdomain=!!this.opts.xd,t.xscheme=!!this.opts.xs;var r=this.xhr=new J(t);try{r.open(this.method,this.uri,this.async);try{if(this.opts.extraHeaders)for(var n in r.setDisableHeaderCheck&&r.setDisableHeaderCheck(!0),this.opts.extraHeaders)this.opts.extraHeaders.hasOwnProperty(n)&&r.setRequestHeader(n,this.opts.extraHeaders[n])}catch(e){}if("POST"===this.method)try{r.setRequestHeader("Content-type","text/plain;charset=UTF-8")}catch(e){}try{r.setRequestHeader("Accept","*/*")}catch(e){}"withCredentials"in r&&(r.withCredentials=this.opts.withCredentials),this.opts.requestTimeout&&(r.timeout=this.opts.requestTimeout),r.onreadystatechange=function(){4===r.readyState&&(200===r.status||1223===r.status?e.onLoad():e.setTimeoutFn((function(){e.onError("number"==typeof r.status?r.status:0)}),0))},r.send(this.data)}catch(t){return void this.setTimeoutFn((function(){e.onError(t)}),0)}"undefined"!=typeof document&&(this.index=o.requestsCount++,o.requests[this.index]=this)}},{key:"onError",value:function(e){this.emitReserved("error",e,this.xhr),this.cleanup(!0)}},{key:"cleanup",value:function(e){if(void 0!==this.xhr&&null!==this.xhr){if(this.xhr.onreadystatechange=K,e)try{this.xhr.abort()}catch(e){}"undefined"!=typeof document&&delete o.requests[this.index],this.xhr=null}}},{key:"onLoad",value:function(){var e=this.xhr.responseText;null!==e&&(this.emitReserved("data",e),this.emitReserved("success"),this.cleanup())}},{key:"abort",value:function(){this.cleanup()}}]),o}(P);if(Z.requestsCount=0,Z.requests={},"undefined"!=typeof document)if("function"==typeof attachEvent)attachEvent("onunload",ee);else if("function"==typeof addEventListener){addEventListener("onpagehide"in C?"pagehide":"unload",ee,!1)}function ee(){for(var e in Z.requests)Z.requests.hasOwnProperty(e)&&Z.requests[e].abort()}var te="function"==typeof Promise&&"function"==typeof Promise.resolve?function(e){return Promise.resolve().then(e)}:function(e,t){return t(e,0)},re=C.WebSocket||C.MozWebSocket,ne="undefined"!=typeof navigator&&"string"==typeof navigator.product&&"reactnative"===navigator.product.toLowerCase(),oe=function(e){i(o,e);var r=f(o);function o(e){var n;return t(this,o),(n=r.call(this,e)).supportsBinary=!e.forceBase64,n}return n(o,[{key:"doOpen",value:function(){if(this.check()){var e=this.uri(),t=this.opts.protocols,r=ne?{}:q(this.opts,"agent","perMessageDeflate","pfx","key","passphrase","cert","ca","ciphers","rejectUnauthorized","localAddress","protocolVersion","origin","maxPayload","family","checkServerIdentity");this.opts.extraHeaders&&(r.headers=this.opts.extraHeaders);try{this.ws=ne?new re(e,t,r):t?new re(e,t):new re(e)}catch(e){return this.emitReserved("error",e)}this.ws.binaryType=this.socket.binaryType||"arraybuffer",this.addEventListeners()}}},{key:"addEventListeners",value:function(){var e=this;this.ws.onopen=function(){e.opts.autoUnref&&e.ws._socket.unref(),e.onOpen()},this.ws.onclose=function(t){return e.onClose({description:"websocket connection closed",context:t})},this.ws.onmessage=function(t){return e.onData(t.data)},this.ws.onerror=function(t){return e.onError("websocket error",t)}}},{key:"write",value:function(e){var t=this;this.writable=!1;for(var r=function(r){var n=e[r],o=r===e.length-1;k(n,t.supportsBinary,(function(e){try{t.ws.send(e)}catch(e){}o&&te((function(){t.writable=!0,t.emitReserved("drain")}),t.setTimeoutFn)}))},n=0;n<e.length;n++)r(n)}},{key:"doClose",value:function(){void 0!==this.ws&&(this.ws.close(),this.ws=null)}},{key:"uri",value:function(){var e=this.query||{},t=this.opts.secure?"wss":"ws",r="";this.opts.port&&("wss"===t&&443!==Number(this.opts.port)||"ws"===t&&80!==Number(this.opts.port))&&(r=":"+this.opts.port),this.opts.timestampRequests&&(e[this.opts.timestampParam]=X()),this.supportsBinary||(e.b64=1);var n=$(e);return t+"://"+(-1!==this.opts.hostname.indexOf(":")?"["+this.opts.hostname+"]":this.opts.hostname)+r+this.opts.path+(n.length?"?"+n:"")}},{key:"check",value:function(){return!!re}},{key:"name",get:function(){return"websocket"}}]),o}(F),ie={websocket:oe,polling:Y},se=/^(?:(?![^:@\/?#]+:[^:@\/]*@)(http|https|ws|wss):\/\/)?((?:(([^:@\/?#]*)(?::([^:@\/?#]*))?)?@)?((?:[a-f0-9]{0,4}:){2,7}[a-f0-9]{0,4}|[^:\/?#]*)(?::(\d*))?)(((\/(?:[^?#](?![^?#\/]*\.[^?#\/.]+(?:[?#]|$)))*\/?)?([^?#\/]*))(?:\?([^#]*))?(?:#(.*))?)/,ae=["source","protocol","authority","userInfo","user","password","host","port","relative","path","directory","file","query","anchor"];function ue(e){var t=e,r=e.indexOf("["),n=e.indexOf("]");-1!=r&&-1!=n&&(e=e.substring(0,r)+e.substring(r,n).replace(/:/g,";")+e.substring(n,e.length));for(var o,i,s=se.exec(e||""),a={},u=14;u--;)a[ae[u]]=s[u]||"";return-1!=r&&-1!=n&&(a.source=t,a.host=a.host.substring(1,a.host.length-1).replace(/;/g,":"),a.authority=a.authority.replace("[","").replace("]","").replace(/;/g,":"),a.ipv6uri=!0),a.pathNames=function(e,t){var r=/\/{2,9}/g,n=t.replace(r,"/").split("/");"/"!=t.slice(0,1)&&0!==t.length||n.splice(0,1);"/"==t.slice(-1)&&n.splice(n.length-1,1);return n}(0,a.path),a.queryKey=(o=a.query,i={},o.replace(/(?:^|&)([^&=]*)=?([^&]*)/g,(function(e,t,r){t&&(i[t]=r)})),i),a}var ce=function(r){i(a,r);var s=f(a);function a(r){var n,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return t(this,a),(n=s.call(this)).writeBuffer=[],r&&"object"===e(r)&&(i=r,r=null),r?(r=ue(r),i.hostname=r.host,i.secure="https"===r.protocol||"wss"===r.protocol,i.port=r.port,r.query&&(i.query=r.query)):i.host&&(i.hostname=ue(i.host).host),_(h(n),i),n.secure=null!=i.secure?i.secure:"undefined"!=typeof location&&"https:"===location.protocol,i.hostname&&!i.port&&(i.port=n.secure?"443":"80"),n.hostname=i.hostname||("undefined"!=typeof location?location.hostname:"localhost"),n.port=i.port||("undefined"!=typeof location&&location.port?location.port:n.secure?"443":"80"),n.transports=i.transports||["polling","websocket"],n.writeBuffer=[],n.prevBufferLen=0,n.opts=o({path:"/engine.io",agent:!1,withCredentials:!1,upgrade:!0,timestampParam:"t",rememberUpgrade:!1,addTrailingSlash:!0,rejectUnauthorized:!0,perMessageDeflate:{threshold:1024},transportOptions:{},closeOnBeforeunload:!0},i),n.opts.path=n.opts.path.replace(/\/$/,"")+(n.opts.addTrailingSlash?"/":""),"string"==typeof n.opts.query&&(n.opts.query=z(n.opts.query)),n.id=null,n.upgrades=null,n.pingInterval=null,n.pingTimeout=null,n.pingTimeoutTimer=null,"function"==typeof addEventListener&&(n.opts.closeOnBeforeunload&&(n.beforeunloadEventListener=function(){n.transport&&(n.transport.removeAllListeners(),n.transport.close())},addEventListener("beforeunload",n.beforeunloadEventListener,!1)),"localhost"!==n.hostname&&(n.offlineEventListener=function(){n.onClose("transport close",{description:"network connection lost"})},addEventListener("offline",n.offlineEventListener,!1))),n.open(),n}return n(a,[{key:"createTransport",value:function(e){var t=o({},this.opts.query);t.EIO=4,t.transport=e,this.id&&(t.sid=this.id);var r=o({},this.opts.transportOptions[e],this.opts,{query:t,socket:this,hostname:this.hostname,secure:this.secure,port:this.port});return new ie[e](r)}},{key:"open",value:function(){var e,t=this;if(this.opts.rememberUpgrade&&a.priorWebsocketSuccess&&-1!==this.transports.indexOf("websocket"))e="websocket";else{if(0===this.transports.length)return void this.setTimeoutFn((function(){t.emitReserved("error","No transports available")}),0);e=this.transports[0]}this.readyState="opening";try{e=this.createTransport(e)}catch(e){return this.transports.shift(),void this.open()}e.open(),this.setTransport(e)}},{key:"setTransport",value:function(e){var t=this;this.transport&&this.transport.removeAllListeners(),this.transport=e,e.on("drain",this.onDrain.bind(this)).on("packet",this.onPacket.bind(this)).on("error",this.onError.bind(this)).on("close",(function(e){return t.onClose("transport close",e)}))}},{key:"probe",value:function(e){var t=this,r=this.createTransport(e),n=!1;a.priorWebsocketSuccess=!1;var o=function(){n||(r.send([{type:"ping",data:"probe"}]),r.once("packet",(function(e){if(!n)if("pong"===e.type&&"probe"===e.data){if(t.upgrading=!0,t.emitReserved("upgrading",r),!r)return;a.priorWebsocketSuccess="websocket"===r.name,t.transport.pause((function(){n||"closed"!==t.readyState&&(h(),t.setTransport(r),r.send([{type:"upgrade"}]),t.emitReserved("upgrade",r),r=null,t.upgrading=!1,t.flush())}))}else{var o=new Error("probe error");o.transport=r.name,t.emitReserved("upgradeError",o)}})))};function i(){n||(n=!0,h(),r.close(),r=null)}var s=function(e){var n=new Error("probe error: "+e);n.transport=r.name,i(),t.emitReserved("upgradeError",n)};function u(){s("transport closed")}function c(){s("socket closed")}function p(e){r&&e.name!==r.name&&i()}var h=function(){r.removeListener("open",o),r.removeListener("error",s),r.removeListener("close",u),t.off("close",c),t.off("upgrading",p)};r.once("open",o),r.once("error",s),r.once("close",u),this.once("close",c),this.once("upgrading",p),r.open()}},{key:"onOpen",value:function(){if(this.readyState="open",a.priorWebsocketSuccess="websocket"===this.transport.name,this.emitReserved("open"),this.flush(),"open"===this.readyState&&this.opts.upgrade)for(var e=0,t=this.upgrades.length;e<t;e++)this.probe(this.upgrades[e])}},{key:"onPacket",value:function(e){if("opening"===this.readyState||"open"===this.readyState||"closing"===this.readyState)switch(this.emitReserved("packet",e),this.emitReserved("heartbeat"),e.type){case"open":this.onHandshake(JSON.parse(e.data));break;case"ping":this.resetPingTimeout(),this.sendPacket("pong"),this.emitReserved("ping"),this.emitReserved("pong");break;case"error":var t=new Error("server error");t.code=e.data,this.onError(t);break;case"message":this.emitReserved("data",e.data),this.emitReserved("message",e.data)}}},{key:"onHandshake",value:function(e){this.emitReserved("handshake",e),this.id=e.sid,this.transport.query.sid=e.sid,this.upgrades=this.filterUpgrades(e.upgrades),this.pingInterval=e.pingInterval,this.pingTimeout=e.pingTimeout,this.maxPayload=e.maxPayload,this.onOpen(),"closed"!==this.readyState&&this.resetPingTimeout()}},{key:"resetPingTimeout",value:function(){var e=this;this.clearTimeoutFn(this.pingTimeoutTimer),this.pingTimeoutTimer=this.setTimeoutFn((function(){e.onClose("ping timeout")}),this.pingInterval+this.pingTimeout),this.opts.autoUnref&&this.pingTimeoutTimer.unref()}},{key:"onDrain",value:function(){this.writeBuffer.splice(0,this.prevBufferLen),this.prevBufferLen=0,0===this.writeBuffer.length?this.emitReserved("drain"):this.flush()}},{key:"flush",value:function(){if("closed"!==this.readyState&&this.transport.writable&&!this.upgrading&&this.writeBuffer.length){var e=this.getWritablePackets();this.transport.send(e),this.prevBufferLen=e.length,this.emitReserved("flush")}}},{key:"getWritablePackets",value:function(){if(!(this.maxPayload&&"polling"===this.transport.name&&this.writeBuffer.length>1))return this.writeBuffer;for(var e,t=1,r=0;r<this.writeBuffer.length;r++){var n=this.writeBuffer[r].data;if(n&&(t+="string"==typeof(e=n)?function(e){for(var t=0,r=0,n=0,o=e.length;n<o;n++)(t=e.charCodeAt(n))<128?r+=1:t<2048?r+=2:t<55296||t>=57344?r+=3:(n++,r+=4);return r}(e):Math.ceil(1.33*(e.byteLength||e.size))),r>0&&t>this.maxPayload)return this.writeBuffer.slice(0,r);t+=2}return this.writeBuffer}},{key:"write",value:function(e,t,r){return this.sendPacket("message",e,t,r),this}},{key:"send",value:function(e,t,r){return this.sendPacket("message",e,t,r),this}},{key:"sendPacket",value:function(e,t,r,n){if("function"==typeof t&&(n=t,t=void 0),"function"==typeof r&&(n=r,r=null),"closing"!==this.readyState&&"closed"!==this.readyState){(r=r||{}).compress=!1!==r.compress;var o={type:e,data:t,options:r};this.emitReserved("packetCreate",o),this.writeBuffer.push(o),n&&this.once("flush",n),this.flush()}}},{key:"close",value:function(){var e=this,t=function(){e.onClose("forced close"),e.transport.close()},r=function r(){e.off("upgrade",r),e.off("upgradeError",r),t()},n=function(){e.once("upgrade",r),e.once("upgradeError",r)};return"opening"!==this.readyState&&"open"!==this.readyState||(this.readyState="closing",this.writeBuffer.length?this.once("drain",(function(){e.upgrading?n():t()})):this.upgrading?n():t()),this}},{key:"onError",value:function(e){a.priorWebsocketSuccess=!1,this.emitReserved("error",e),this.onClose("transport error",e)}},{key:"onClose",value:function(e,t){"opening"!==this.readyState&&"open"!==this.readyState&&"closing"!==this.readyState||(this.clearTimeoutFn(this.pingTimeoutTimer),this.transport.removeAllListeners("close"),this.transport.close(),this.transport.removeAllListeners(),"function"==typeof removeEventListener&&(removeEventListener("beforeunload",this.beforeunloadEventListener,!1),removeEventListener("offline",this.offlineEventListener,!1)),this.readyState="closed",this.id=null,this.emitReserved("close",e,t),this.writeBuffer=[],this.prevBufferLen=0)}},{key:"filterUpgrades",value:function(e){for(var t=[],r=0,n=e.length;r<n;r++)~this.transports.indexOf(e[r])&&t.push(e[r]);return t}}]),a}(P);ce.protocol=4;return function(e,t){return new ce(e,t)}}));
//# sourceMappingURL=engine.io.min.js.map
