{"name": "engine.io-client", "description": "Client for the realtime Engine", "license": "MIT", "version": "6.4.0", "main": "./build/cjs/index.js", "module": "./build/esm/index.js", "exports": {"./package.json": "./package.json", "./dist/engine.io.esm.min.js": "./dist/engine.io.esm.min.js", "./dist/engine.io.js": "./dist/engine.io.js", "./dist/engine.io.min.js": "./dist/engine.io.min.js", ".": {"import": {"node": "./build/esm-debug/index.js", "default": "./build/esm/index.js"}, "require": "./build/cjs/index.js"}}, "types": "build/esm/index.d.ts", "homepage": "https://github.com/socketio/engine.io-client", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "web": "https://github.com/cadorn"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"@socket.io/component-emitter": "~3.1.0", "debug": "~4.3.1", "engine.io-parser": "~5.0.3", "ws": "~8.11.0", "xmlhttprequest-ssl": "~2.0.0"}, "devDependencies": {"@babel/core": "^7.12.9", "@babel/plugin-transform-object-assign": "^7.12.1", "@babel/preset-env": "^7.12.7", "@rollup/plugin-babel": "^5.3.0", "@rollup/plugin-commonjs": "^21.0.0", "@rollup/plugin-node-resolve": "^13.0.5", "@sinonjs/fake-timers": "^7.1.2", "@types/mocha": "^9.0.0", "@types/node": "^16.10.1", "@types/sinonjs__fake-timers": "^6.0.3", "babel-loader": "^8.2.2", "blob": "0.0.5", "engine.io": "^6.2.0-alpha.1", "expect.js": "^0.3.1", "express": "^4.17.1", "mocha": "^3.2.0", "prettier": "^2.8.1", "rollup": "^2.58.0", "rollup-plugin-terser": "^7.0.2", "socket.io-browsers": "~1.0.4", "typescript": "^4.4.3", "webpack": "^4.44.2", "webpack-cli": "^3.3.12", "webpack-remove-debug": "^0.1.0", "zuul": "~3.11.1", "zuul-builder-webpack": "^1.2.0", "zuul-ngrok": "4.0.0"}, "scripts": {"compile": "rimraf ./build && tsc && tsc -p tsconfig.esm.json && ./postcompile.sh", "test": "npm run format:check && npm run compile && if test \"$BROWSERS\" = \"1\" ; then npm run test:browser; else npm run test:node; fi", "test:node": "mocha --bail --reporter dot --require test/support/server.js test/index.js", "test:browser": "zuul test/index.js", "build": "rollup -c support/rollup.config.umd.js && rollup -c support/rollup.config.esm.js", "format:check": "prettier --check 'lib/**/*.ts' 'test/**/*.js' 'support/**/*.js'", "format:fix": "prettier --write 'lib/**/*.ts' 'test/**/*.js' 'support/**/*.js'", "prepack": "npm run compile"}, "browser": {"./test/node.js": false, "./build/esm/transports/xmlhttprequest.js": "./build/esm/transports/xmlhttprequest.browser.js", "./build/esm/transports/websocket-constructor.js": "./build/esm/transports/websocket-constructor.browser.js", "./build/esm/globalThis.js": "./build/esm/globalThis.browser.js", "./build/cjs/transports/xmlhttprequest.js": "./build/cjs/transports/xmlhttprequest.browser.js", "./build/cjs/transports/websocket-constructor.js": "./build/cjs/transports/websocket-constructor.browser.js", "./build/cjs/globalThis.js": "./build/cjs/globalThis.browser.js"}, "repository": {"type": "git", "url": "https://github.com/socketio/engine.io-client.git"}, "files": ["build/", "dist/"]}