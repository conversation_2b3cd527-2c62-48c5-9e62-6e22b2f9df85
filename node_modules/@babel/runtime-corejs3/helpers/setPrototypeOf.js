var _Object$setPrototypeOf = require("@babel/runtime-corejs3/core-js/object/set-prototype-of");
var _bindInstanceProperty = require("@babel/runtime-corejs3/core-js/instance/bind");
function _setPrototypeOf(o, p) {
  var _context;
  module.exports = _setPrototypeOf = _Object$setPrototypeOf ? _bindInstanceProperty(_context = _Object$setPrototypeOf).call(_context) : function _setPrototypeOf(o, p) {
    o.__proto__ = p;
    return o;
  }, module.exports.__esModule = true, module.exports["default"] = module.exports;
  return _setPrototypeOf(o, p);
}
module.exports = _setPrototypeOf, module.exports.__esModule = true, module.exports["default"] = module.exports;