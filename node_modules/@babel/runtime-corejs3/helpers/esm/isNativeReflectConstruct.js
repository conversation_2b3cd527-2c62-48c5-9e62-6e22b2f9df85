import _Reflect$construct from "@babel/runtime-corejs3/core-js/reflect/construct";
export default function _isNativeReflectConstruct() {
  if (typeof Reflect === "undefined" || !_Reflect$construct) return false;
  if (_Reflect$construct.sham) return false;
  if (typeof Proxy === "function") return true;
  try {
    Boolean.prototype.valueOf.call(_Reflect$construct(<PERSON><PERSON><PERSON>, [], function () {}));
    return true;
  } catch (e) {
    return false;
  }
}