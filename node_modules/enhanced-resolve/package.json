{"name": "enhanced-resolve", "version": "5.12.0", "author": "<PERSON> @sokra", "description": "Offers a async require.resolve function. It's highly configurable.", "files": ["lib", "types.d.ts", "LICENSE"], "browser": {"pnpapi": false, "process": "./lib/util/process-browser.js"}, "dependencies": {"graceful-fs": "^4.2.4", "tapable": "^2.2.0"}, "license": "MIT", "devDependencies": {"@types/mocha": "^8.0.3", "@types/node": "^14.11.1", "cspell": "4.2.8", "eslint": "^7.9.0", "eslint-config-prettier": "^6.11.0", "eslint-plugin-jsdoc": "^30.5.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prettier": "^3.1.4", "husky": "^6.0.0", "lint-staged": "^10.4.0", "memfs": "^3.2.0", "mocha": "^8.1.3", "nyc": "^15.1.0", "prettier": "^2.1.2", "should": "^13.2.3", "tooling": "webpack/tooling#v1.14.0", "typescript": "^4.2.0-beta"}, "engines": {"node": ">=10.13.0"}, "main": "lib/index.js", "types": "types.d.ts", "homepage": "http://github.com/webpack/enhanced-resolve", "scripts": {"lint": "yarn run code-lint && yarn run type-lint && yarn run special-lint && yarn run spelling", "fix": "yarn run code-lint-fix && yarn run special-lint-fix", "code-lint": "eslint --cache lib test", "code-lint-fix": "eslint --cache lib test --fix", "type-lint": "tsc", "special-lint": "node node_modules/tooling/lockfile-lint && node node_modules/tooling/inherit-types && node node_modules/tooling/format-file-header && node node_modules/tooling/generate-types", "special-lint-fix": "node node_modules/tooling/inherit-types --write && node node_modules/tooling/format-file-header --write && node node_modules/tooling/generate-types --write", "pretty": "prettier --loglevel warn --write \"lib/**/*.{js,json}\" \"test/*.js\"", "pretest": "yarn lint", "spelling": "cspell \"**/*.*\"", "test": "mocha --full-trace --check-leaks", "test:only": "mocha --full-trace --check-leaks", "precover": "yarn lint", "cover": "nyc --reporter=html node node_modules/mocha/bin/_mocha --full-trace --check-leaks", "cover:ci": "nyc --reporter=lcovonly node node_modules/mocha/bin/_mocha --full-trace --check-leaks", "prepare": "husky install"}, "lint-staged": {"*.js": "eslint --cache"}, "repository": {"type": "git", "url": "git://github.com/webpack/enhanced-resolve.git"}}